# 🚀 Plano de Implementação: XTTS-v2 para Audiobooks

## 📋 Resumo do Projeto Atual

### ✅ Status Atual (Sistema Kokoro TTS)
- **Sistema funcionando:** Gerador de audiobooks completo
- **Voz atual:** `pf_dora` (brasileira, mas com limitações)
- **Problema identificado:** Voz ainda não soa 100% natural
- **Arquitetura:** Modular e bem estruturada

### 🎯 Objetivo da Implementação
Adicionar **XTTS-v2** como engine principal de TTS, mantendo Kokoro como fallback, para obter:
- Vozes muito mais naturais em português brasileiro
- Capacidade de voice cloning com apenas 6 segundos de áudio
- Qualidade comercial/profissional

## 🔍 Análise Técnica do XTTS-v2

### ✅ Vantagens Confirmadas
- **Português nativo:** Treinado especificamente para pt-BR
- **Voice cloning:** Clona qualquer voz com 6+ segundos de áudio
- **Qualidade superior:** Usado comercialmente pela Coqui
- **Totalmente offline:** Após download inicial (~1.8GB)
- **Cross-language:** Pode clonar vozes entre idiomas
- **API simples:** Integração fácil com código existente

### 📊 Especificações Técnicas
- **Modelo:** `tts_models/multilingual/multi-dataset/xtts_v2`
- **Taxa de amostragem:** 24kHz (igual ao Kokoro)
- **Idiomas:** 17 idiomas incluindo português
- **Tamanho:** ~1.8GB (vs ~327MB do Kokoro)
- **Licença:** Coqui Public Model License (gratuito para uso pessoal)

## 🏗️ Arquitetura da Implementação

### 📁 Estrutura de Arquivos (Manter Existente + Adicionar)
```
Audiobook_Generator/
├── src/
│   ├── gerador_audiobook.py       # MODIFICAR: Adicionar XTTS-v2
│   ├── gerador_xtts.py           # NOVO: Engine XTTS-v2
│   ├── processador_notas.py       # MANTER: Sem alterações
│   ├── configuracao.py            # MODIFICAR: Adicionar configs XTTS
│   └── utils.py                   # MANTER: Sem alterações
├── config/
│   └── config.yaml                # MODIFICAR: Adicionar engine selection
├── voices/                        # NOVO: Pasta para vozes de referência
│   ├── brasileira_feminina.wav    # NOVO: Voz de referência
│   └── brasileira_masculina.wav   # NOVO: Voz de referência
└── main.py                        # MODIFICAR: Adicionar opções XTTS
```

### 🔧 Modificações Necessárias

#### 1. **Configuração (config.yaml)**
```yaml
# Engine de TTS (novo)
engine:
  primary: 'xtts_v2'      # xtts_v2 ou kokoro
  fallback: 'kokoro'      # fallback se primary falhar

# Configurações XTTS-v2 (novo)
xtts_v2:
  model_name: 'tts_models/multilingual/multi-dataset/xtts_v2'
  language: 'pt'
  speaker_wav: 'voices/brasileira_feminina.wav'  # voz padrão
  speed: 1.0
  temperature: 0.75       # controle de variação
  length_penalty: 1.0     # controle de duração
  repetition_penalty: 5.0 # evitar repetições

# Manter configurações Kokoro existentes
tts:
  lang_code: 'p'
  voice: 'pf_dora'
  speed: 0.85
  # ... resto igual
```

#### 2. **Novo Engine XTTS-v2 (gerador_xtts.py)**
```python
class GeradorXTTS:
    def __init__(self, configuracoes):
        self.config = configuracoes
        self.tts = None
        self._inicializar_xtts()
    
    def _inicializar_xtts(self):
        from TTS.api import TTS
        self.tts = TTS(self.config['model_name'])
    
    def gerar_audio_segmento(self, texto: str) -> np.ndarray:
        # Implementar geração com XTTS-v2
        pass
    
    def clonar_voz(self, texto: str, speaker_wav: str) -> np.ndarray:
        # Implementar voice cloning
        pass
```

#### 3. **Modificar Gerador Principal (gerador_audiobook.py)**
```python
class GeradorAudiobook:
    def __init__(self, configuracoes):
        self.config = configuracoes
        self.engine_primary = self._criar_engine(config['engine']['primary'])
        self.engine_fallback = self._criar_engine(config['engine']['fallback'])
    
    def _criar_engine(self, tipo):
        if tipo == 'xtts_v2':
            return GeradorXTTS(self.config['xtts_v2'])
        elif tipo == 'kokoro':
            return GeradorKokoro(self.config['tts'])  # engine atual
    
    def gerar_audio_segmento(self, texto: str):
        try:
            return self.engine_primary.gerar_audio_segmento(texto)
        except Exception as e:
            logger.warning(f"Engine primary falhou: {e}, usando fallback")
            return self.engine_fallback.gerar_audio_segmento(texto)
```

#### 4. **Interface CLI (main.py)**
```bash
# Novos comandos
python main.py arquivo "nota.md" --engine xtts_v2
python main.py arquivo "nota.md" --clone-voice "minha_voz.wav"
python main.py arquivo "nota.md" --voice-ref "voices/brasileira_feminina.wav"
```

## 📦 Dependências Adicionais

### requirements.txt (Adicionar)
```
# XTTS-v2 dependencies
TTS>=0.22.0
torch>=2.0.0
torchaudio>=2.0.0
transformers>=4.33.0
```

### Instalação
```bash
pip install TTS>=0.22.0
```

## 🎵 Vozes de Referência Brasileiras

### 📁 Pasta voices/ (Criar)
- **brasileira_feminina.wav** - Voz feminina brasileira natural (6-10 segundos)
- **brasileira_masculina.wav** - Voz masculina brasileira natural (6-10 segundos)
- **locutora_profissional.wav** - Voz de locutora profissional (opcional)

### 🎤 Como Obter Vozes de Referência
1. **Gravar própria voz** (6-10 segundos, português claro)
2. **Extrair de vídeos brasileiros** (YouTube, podcasts)
3. **Usar samples de locutores** (com permissão)

## 🧪 Plano de Testes

### 1. **Teste Básico**
```python
# Testar XTTS-v2 isoladamente
from TTS.api import TTS
tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2")
tts.tts_to_file("Teste em português brasileiro", 
                speaker_wav="voices/brasileira_feminina.wav",
                language="pt", file_path="teste_xtts.wav")
```

### 2. **Teste Comparativo**
- Mesmo texto com Kokoro vs XTTS-v2
- Avaliar naturalidade, pronúncia, fluidez
- Testar diferentes vozes de referência

### 3. **Teste de Performance**
- Tempo de processamento vs Kokoro
- Uso de memória
- Qualidade de áudio

## 🚀 Cronograma de Implementação

### Fase 1: Setup e Testes (30 min)
1. Instalar dependências XTTS-v2
2. Testar modelo básico
3. Preparar vozes de referência

### Fase 2: Implementação Core (2 horas)
1. Criar `gerador_xtts.py`
2. Modificar `configuracao.py`
3. Atualizar `gerador_audiobook.py`

### Fase 3: Interface e CLI (1 hora)
1. Adicionar opções ao `main.py`
2. Atualizar documentação
3. Criar exemplos de uso

### Fase 4: Testes e Refinamento (1 hora)
1. Testes comparativos
2. Ajustes de configuração
3. Documentação final

## 📝 Comandos de Implementação

### Para Nova Conversa
```
Contexto: Sistema de audiobooks funcionando com Kokoro TTS
Objetivo: Implementar XTTS-v2 como engine principal
Arquivo de referência: PLANO_XTTS_V2.md
Manter: Toda arquitetura existente
Adicionar: Engine XTTS-v2 com voice cloning
```

## 🎯 Resultado Esperado

### ✅ Sistema Final
- **Dual-engine:** XTTS-v2 (principal) + Kokoro (fallback)
- **Voice cloning:** Vozes brasileiras naturais personalizadas
- **Qualidade superior:** Audiobooks com qualidade profissional
- **Flexibilidade:** Escolha de engine via CLI
- **Compatibilidade:** Mantém toda funcionalidade existente

### 🎧 Benefícios para o Usuário
- **Vozes muito mais naturais** em português brasileiro
- **Personalização total** com voice cloning
- **Qualidade profissional** para estudos
- **Sistema robusto** com fallback automático

---

**📌 IMPORTANTE:** Este plano mantém toda a arquitetura existente e adiciona XTTS-v2 como uma camada superior, garantindo compatibilidade total com o sistema atual.
