#!/usr/bin/env python3
"""
Script para baixar vozes de referência brasileiras para XTTS-v2
"""

import os
import sys
import requests
from pathlib import Path
import tempfile

def baixar_vozes_mozilla_common_voice():
    """Baixa algumas amostras do Common Voice Mozilla em português brasileiro"""
    
    print("🎤 Baixando vozes de referência do Common Voice Mozilla...")
    
    # URLs de exemplo do Common Voice (estas são URLs fictícias - precisaríamos das reais)
    # Vou criar uma abordagem diferente
    
    # Verificar se há vozes de exemplo no próprio XTTS-v2
    try:
        from TTS.api import TTS
        
        print("🔍 Verificando vozes de exemplo do XTTS-v2...")
        
        # Inicializar TTS
        tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2")
        
        # Verificar se há speakers pré-definidos
        if hasattr(tts.tts, 'speakers') and tts.tts.speakers:
            print(f"📋 Speakers encontrados: {len(tts.tts.speakers)}")
            for i, speaker in enumerate(tts.tts.speakers[:10]):  # Primeiros 10
                print(f"   {i+1}. {speaker}")
            return True
        else:
            print("⚠️ Nenhum speaker pré-definido encontrado")
            return False
            
    except Exception as e:
        print(f"❌ Erro ao verificar speakers: {e}")
        return False

def criar_voz_referencia_melhorada():
    """Cria uma voz de referência melhorada usando técnicas mais avançadas"""
    
    print("🔧 Criando voz de referência melhorada...")
    
    try:
        import numpy as np
        import soundfile as sf
        from scipy import signal
        
        # Parâmetros para voz mais realista
        sample_rate = 24000
        duration = 6.0  # 6 segundos como recomendado
        
        # Criar múltiplas frequências que simulam voz humana
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        
        # Frequências fundamentais típicas de voz humana
        f0 = 150  # Frequência fundamental (Hz) - voz masculina média
        
        # Criar harmônicos
        audio = np.zeros_like(t)
        
        # Fundamental
        audio += 0.4 * np.sin(2 * np.pi * f0 * t)
        
        # Harmônicos
        for harmonic in [2, 3, 4, 5]:
            amplitude = 0.4 / harmonic  # Amplitude decresce com harmônicos
            audio += amplitude * np.sin(2 * np.pi * f0 * harmonic * t)
        
        # Adicionar formantes (ressonâncias da voz)
        # Formantes típicos para vogais portuguesas
        formant_freqs = [800, 1200, 2500]  # Aproximação para /a/
        
        for freq in formant_freqs:
            formant = 0.1 * np.sin(2 * np.pi * freq * t)
            audio += formant
        
        # Envelope de amplitude mais natural
        # Simular respiração e variações naturais
        envelope = np.ones_like(t)
        
        # Variações suaves de amplitude
        for i in range(3):
            freq_var = 0.5 + i * 0.3
            envelope += 0.1 * np.sin(2 * np.pi * freq_var * t)
        
        # Aplicar envelope
        audio = audio * envelope
        
        # Normalizar
        audio = audio / np.max(np.abs(audio)) * 0.8
        
        # Aplicar filtro passa-banda para simular características vocais
        # Filtro para faixa de frequência da voz humana (80Hz - 8kHz)
        nyquist = sample_rate / 2
        low_freq = 80 / nyquist
        high_freq = 8000 / nyquist
        
        b, a = signal.butter(4, [low_freq, high_freq], btype='band')
        audio = signal.filtfilt(b, a, audio)
        
        # Salvar arquivo
        pasta_voices = Path("voices")
        pasta_voices.mkdir(exist_ok=True)
        
        arquivo_voz = pasta_voices / "voz_brasileira_sintetica_melhorada.wav"
        sf.write(str(arquivo_voz), audio, sample_rate)
        
        print(f"✅ Voz de referência melhorada criada: {arquivo_voz}")
        return str(arquivo_voz)
        
    except Exception as e:
        print(f"❌ Erro ao criar voz melhorada: {e}")
        return None

def baixar_amostra_youtube():
    """Sugere como baixar amostras de voz do YouTube"""
    
    print("\n💡 ALTERNATIVA: Baixar amostras do YouTube")
    print("=" * 50)
    print("Você pode baixar amostras de voz brasileira de:")
    print()
    print("1. 📺 Canais de notícias (Globo, SBT, Record)")
    print("2. 🎙️ Podcasts brasileiros")
    print("3. 📚 Audiobooks em português")
    print("4. 🎬 Entrevistas e documentários")
    print()
    print("🔧 Como fazer:")
    print("1. Use yt-dlp ou similar para baixar áudio")
    print("2. Corte um trecho de 6-10 segundos limpo")
    print("3. Salve como .wav na pasta voices/")
    print("4. Use: --voice-ref voices/sua_voz.wav")
    print()
    print("📋 Exemplo de comando:")
    print('python main.py arquivo "resumo.md" --voice-ref "voices/sua_voz.wav"')

def main():
    """Função principal"""
    
    print("🎭 Configurador de Vozes de Referência XTTS-v2")
    print("=" * 50)
    
    # Verificar speakers embutidos
    if baixar_vozes_mozilla_common_voice():
        print("✅ Speakers embutidos encontrados!")
    
    # Criar voz melhorada
    voz_melhorada = criar_voz_referencia_melhorada()
    
    if voz_melhorada:
        print(f"\n🎯 Teste a voz melhorada:")
        print(f'python main.py arquivo "amostra_xtts_v2.md" --voice-ref "{voz_melhorada}"')
    
    # Sugestões para vozes reais
    baixar_amostra_youtube()
    
    print("\n" + "=" * 50)
    print("🎊 Configuração concluída!")
    print()
    print("💡 RECOMENDAÇÃO:")
    print("Para melhor qualidade, use uma gravação real de 6 segundos")
    print("de uma voz brasileira falando português naturalmente.")

if __name__ == "__main__":
    main()
