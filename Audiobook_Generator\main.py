#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sistema de Geração de Audiobooks - Interface Principal
Sistema Dual-Engine: XTTS-v2 + Kokoro TTS para máxima qualidade
"""

import sys
import os
import click
import logging
from pathlib import Path
from typing import Optional

# Configurar encoding para Windows
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter("utf-8")(sys.stdout.detach())
    sys.stderr = codecs.getwriter("utf-8")(sys.stderr.detach())

# Adicionar src ao path
sys.path.append(str(Path(__file__).parent / "src"))

from gerador_audiobook import GeradorAudiobook
from configuracao import ConfiguradorAudiobook
from utils import configurar_logging, validar_caminho, criar_estrutura_diretorios, formatar_duracao

# Configuração global
VERSAO = "2.0.0"
NOME_APP = "Gerador de Audiobooks - Sistema Dual Engine"


@click.group()
@click.version_option(version=VERSAO, prog_name=NOME_APP)
@click.option('--config', '-c', type=click.Path(exists=True), 
              help='Caminho para arquivo de configuração personalizado')
@click.option('--verbose', '-v', is_flag=True, help='Modo verboso (debug)')
@click.option('--quiet', '-q', is_flag=True, help='Modo silencioso (apenas erros)')
@click.pass_context
def cli(ctx, config, verbose, quiet):
    """
    Sistema de Geração de Audiobooks - Dual Engine

    Converte suas notas do Obsidian em audiobooks de qualidade excepcional
    usando XTTS-v2 com voice cloning e Kokoro TTS como fallback.
    """
    # Configurar contexto
    ctx.ensure_object(dict)
    
    # Configurar logging
    if verbose:
        nivel_log = 'DEBUG'
    elif quiet:
        nivel_log = 'ERROR'
    else:
        nivel_log = 'INFO'
    
    configurar_logging(nivel_log)
    
    # Carregar configurações
    arquivo_config = Path(config) if config else None
    configurador = ConfiguradorAudiobook(arquivo_config)
    
    # Validar configurações
    erros = configurador.validar_configuracoes()
    if erros:
        click.echo("❌ Erros nas configurações:", err=True)
        for erro in erros:
            click.echo(f"   - {erro}", err=True)
        sys.exit(1)
    
    # Armazenar no contexto
    ctx.obj['configurador'] = configurador
    ctx.obj['config_tts'] = configurador.obter_configuracoes_tts()


@cli.command()
@click.argument('arquivo', type=click.Path(exists=True))
@click.option('--output', '-o', type=click.Path(),
              help='Pasta de saída (padrão: output/audiobooks)')
@click.option('--voice', help='Voz a ser usada (Kokoro: af_heart, af_sky)')
@click.option('--speed', type=float, help='Velocidade da fala (0.5 - 2.0)')
@click.option('--engine', type=click.Choice(['xtts_v2', 'kokoro', 'auto']),
              default='auto', help='Engine de TTS (padrão: auto)')
@click.option('--clone-voice', type=click.Path(exists=True),
              help='Arquivo de voz para cloning (XTTS-v2 apenas)')
@click.option('--voice-ref', type=click.Path(exists=True),
              help='Voz de referência da pasta voices/ (XTTS-v2)')
@click.pass_context
def arquivo(ctx, arquivo, output, voice, speed, engine, clone_voice, voice_ref):
    """
    Gera audiobook para um arquivo específico

    ARQUIVO: Caminho para o arquivo .md do Obsidian

    Exemplos:
    \b
    # Usar engine automático (XTTS-v2 + fallback Kokoro)
    python main.py arquivo "nota.md"

    # Forçar uso do XTTS-v2 com voice cloning
    python main.py arquivo "nota.md" --engine xtts_v2 --clone-voice "minha_voz.wav"

    # Usar voz de referência da pasta voices/
    python main.py arquivo "nota.md" --voice-ref "voices/brasileira_feminina.wav"

    # Forçar uso do Kokoro
    python main.py arquivo "nota.md" --engine kokoro --voice af_heart
    """
    try:
        # Obter configurações
        configurador = ctx.obj['configurador']
        config_completa = configurador.configuracoes.copy()

        # Aplicar opções da linha de comando
        if engine and engine != 'auto':
            # Forçar engine específico
            config_completa['engine']['primary'] = engine
            config_completa['engine']['fallback'] = 'kokoro' if engine == 'xtts_v2' else 'xtts_v2'

        # Opções específicas do Kokoro
        if voice:
            config_completa['tts']['voice'] = voice
        if speed:
            if engine == 'kokoro' or config_completa['engine']['primary'] == 'kokoro':
                config_completa['tts']['speed'] = speed
            else:
                config_completa['xtts_v2']['speed'] = speed

        # Opções específicas do XTTS-v2
        speaker_wav_path = None
        if clone_voice:
            speaker_wav_path = str(clone_voice)
            click.echo(f"🎤 Voice cloning: {Path(clone_voice).name}")
        elif voice_ref:
            speaker_wav_path = str(voice_ref)
            config_completa['xtts_v2']['speaker_wav'] = speaker_wav_path
            click.echo(f"🎭 Voz de referência: {Path(voice_ref).name}")

        # Definir pasta de saída
        if output:
            pasta_saida = Path(output)
        else:
            caminhos = configurador.obter_caminhos()
            pasta_saida = caminhos['pasta_saida']

        # Validar arquivo
        arquivo_path = validar_caminho(arquivo, deve_existir=True)
        if not arquivo_path.suffix.lower() == '.md':
            click.echo("❌ Arquivo deve ter extensão .md", err=True)
            sys.exit(1)

        # Mostrar informações
        engine_ativo = config_completa['engine']['primary']
        click.echo(f"🚀 Gerando audiobook para: {arquivo_path.name}")
        click.echo(f"📁 Pasta de saída: {pasta_saida}")
        click.echo(f"🎛️ Engine: {engine_ativo.upper()}")

        if engine_ativo == 'xtts_v2':
            speed_val = config_completa['xtts_v2']['speed']
            click.echo(f"⚡ Velocidade: {speed_val}x")
            if speaker_wav_path:
                click.echo(f"🎤 Voice cloning ativo")
        else:
            voice_val = config_completa['tts']['voice']
            speed_val = config_completa['tts']['speed']
            click.echo(f"🎵 Voz: {voice_val}")
            click.echo(f"⚡ Velocidade: {speed_val}x")

        # Gerar audiobook
        gerador = GeradorAudiobook(config_completa)

        # Usar speaker_wav se especificado
        if speaker_wav_path and hasattr(gerador, 'engine_primary'):
            # Passar speaker_wav para o método de geração
            info_audiobook = gerador.gerar_audiobook_arquivo(arquivo_path, pasta_saida, speaker_wav=speaker_wav_path)
        else:
            info_audiobook = gerador.gerar_audiobook_arquivo(arquivo_path, pasta_saida)
        
        # Mostrar resultados
        click.echo("\n🎉 Audiobook gerado com sucesso!")
        click.echo(f"📁 Arquivo: {info_audiobook['arquivo_saida']}")
        click.echo(f"⏱️ Duração: {info_audiobook['duracao_formatada']}")
        click.echo(f"📊 Palavras: {info_audiobook['palavras']:,}")
        click.echo(f"🔀 Segmentos: {info_audiobook['segmentos']}")
        click.echo(f"📏 Tamanho: {info_audiobook['tamanho_arquivo']}")
        click.echo(f"⚡ Tempo de processamento: {info_audiobook['tempo_processamento_formatado']}")
        
    except Exception as e:
        click.echo(f"❌ Erro: {e}", err=True)
        if ctx.obj.get('verbose'):
            import traceback
            traceback.print_exc()
        sys.exit(1)


@cli.command()
@click.argument('pasta', type=click.Path(exists=True))
@click.option('--output', '-o', type=click.Path(), 
              help='Pasta de saída (padrão: output/audiobooks)')
@click.option('--voice', help='Voz a ser usada')
@click.option('--speed', type=float, help='Velocidade da fala')
@click.option('--filtro', default='*.md', help='Filtro de arquivos (padrão: *.md)')
@click.pass_context
def pasta(ctx, pasta, output, voice, speed, filtro):
    """
    Gera audiobooks para todos os arquivos de uma pasta
    
    PASTA: Caminho para a pasta com arquivos .md
    """
    try:
        # Obter configurações
        configurador = ctx.obj['configurador']
        config_tts = ctx.obj['config_tts'].copy()
        
        # Aplicar opções da linha de comando
        if voice:
            config_tts['voice'] = voice
        if speed:
            config_tts['speed'] = speed
        
        # Definir pasta de saída
        if output:
            pasta_saida = Path(output)
        else:
            caminhos = configurador.obter_caminhos()
            pasta_saida = caminhos['pasta_saida']
        
        # Validar pasta
        pasta_origem = validar_caminho(pasta, deve_existir=True)
        
        click.echo(f"🚀 Processando pasta: {pasta_origem}")
        click.echo(f"📁 Pasta de saída: {pasta_saida}")
        click.echo(f"🎵 Voz: {config_tts['voice']}")
        click.echo(f"⚡ Velocidade: {config_tts['speed']}x")
        click.echo(f"🔍 Filtro: {filtro}")
        
        # Confirmar se há muitos arquivos
        arquivos = list(pasta_origem.glob(filtro))
        if len(arquivos) > 10:
            if not click.confirm(f"Encontrados {len(arquivos)} arquivos. Continuar?"):
                click.echo("Operação cancelada.")
                return
        
        # Gerar audiobooks
        gerador = GeradorAudiobook(config_tts)
        audiobooks = gerador.gerar_audiobook_pasta(pasta_origem, pasta_saida)
        
        # Mostrar resultados
        if audiobooks:
            total_duracao = sum(ab['duracao_audio'] for ab in audiobooks)
            total_palavras = sum(ab['palavras'] for ab in audiobooks)
            
            click.echo(f"\n🎉 Processamento concluído!")
            click.echo(f"📚 Audiobooks gerados: {len(audiobooks)}")
            click.echo(f"⏱️ Duração total: {formatar_duracao(total_duracao)}")
            click.echo(f"📝 Total de palavras: {total_palavras:,}")
            click.echo(f"📁 Pasta de saída: {pasta_saida}")
        else:
            click.echo("❌ Nenhum audiobook foi gerado.")
        
    except Exception as e:
        click.echo(f"❌ Erro: {e}", err=True)
        if ctx.obj.get('verbose'):
            import traceback
            traceback.print_exc()
        sys.exit(1)


@cli.command()
@click.pass_context
def vozes(ctx):
    """
    Gerencia vozes de referência para XTTS-v2

    Lista vozes disponíveis na pasta voices/ e permite testar voice cloning.
    """
    try:
        click.echo("🎭 Gerenciador de Vozes XTTS-v2")
        click.echo("=" * 40)

        # Verificar pasta voices/
        pasta_voices = Path("voices")
        if not pasta_voices.exists():
            click.echo("📁 Criando pasta voices/...")
            pasta_voices.mkdir(exist_ok=True)

        # Verificar pasta amostras/ (suas vozes)
        pasta_amostras = Path("amostras")

        # Listar vozes disponíveis
        extensoes_audio = {'.wav', '.mp3', '.flac', '.ogg'}

        # Vozes da pasta amostras (suas vozes)
        vozes_amostras = []
        if pasta_amostras.exists():
            for arquivo in pasta_amostras.iterdir():
                if arquivo.suffix.lower() in extensoes_audio:
                    vozes_amostras.append(arquivo)

        # Vozes da pasta voices
        vozes_voices = []
        for arquivo in pasta_voices.iterdir():
            if arquivo.suffix.lower() in extensoes_audio:
                vozes_voices.append(arquivo)

        # Mostrar suas amostras primeiro
        if vozes_amostras:
            click.echo(f"🎭 SUAS AMOSTRAS DE VOZ ({len(vozes_amostras)}):")
            for i, voz in enumerate(vozes_amostras, 1):
                tamanho = voz.stat().st_size
                click.echo(f"  {i}. {voz.name} ({tamanho:,} bytes)")

            click.echo(f"\n💡 Como usar suas vozes:")
            click.echo(f'   python main.py arquivo "resumo.md" --clone-voice "amostras/Voz-1.wav"')
            click.echo(f'   python main.py pasta "pasta/" --clone-voice "amostras/Voz-2.wav"')

        # Mostrar vozes da pasta voices
        if vozes_voices:
            click.echo(f"\n🎤 Vozes na pasta voices/ ({len(vozes_voices)}):")
            for i, voz in enumerate(vozes_voices, 1):
                tamanho = voz.stat().st_size
                click.echo(f"  {i}. {voz.name} ({tamanho:,} bytes)")

            click.echo(f"\n💡 Como usar vozes da pasta voices/:")
            click.echo(f'   python main.py arquivo "resumo.md" --voice-ref "voices/sua_voz.wav"')

        # Se não há vozes
        if not vozes_amostras and not vozes_voices:
            click.echo("📭 Nenhuma voz encontrada!")
            click.echo("\n💡 Para usar voice cloning:")
            click.echo("   1. Coloque arquivos de áudio (.wav, .mp3) na pasta amostras/ ou voices/")
            click.echo("   2. Use --clone-voice amostras/Voz-X.wav")
            click.echo("   3. Ou use --voice-ref voices/sua_voz.wav")

        click.echo(f"\n📁 Pastas:")
        click.echo(f"   amostras: {pasta_amostras.absolute()}")
        click.echo(f"   voices: {pasta_voices.absolute()}")

    except Exception as e:
        click.echo(f"❌ Erro: {e}", err=True)
        sys.exit(1)

@cli.command()
@click.pass_context
def config(ctx):
    """
    Mostra configurações atuais do sistema
    """
    configurador = ctx.obj['configurador']
    
    click.echo(f"⚙️ Configurações do {NOME_APP}")
    click.echo("=" * 50)
    
    # Configurações TTS
    click.echo("\n🎵 Text-to-Speech:")
    click.echo(f"   Idioma: {configurador.obter('tts.lang_code')} (Português BR)")
    click.echo(f"   Voz: {configurador.obter('tts.voice')}")
    click.echo(f"   Velocidade: {configurador.obter('tts.speed')}x")
    click.echo(f"   Taxa de amostragem: {configurador.obter('tts.sample_rate')} Hz")
    
    # Configurações de processamento
    click.echo("\n⚙️ Processamento:")
    click.echo(f"   Palavras por segmento: {configurador.obter('processamento.max_palavras_segmento')}")
    click.echo(f"   Pausa entre segmentos: {configurador.obter('processamento.pausa_entre_segmentos')}s")
    click.echo(f"   Formato de saída: {configurador.obter('processamento.formato_saida')}")
    
    # Caminhos
    click.echo("\n📁 Caminhos:")
    caminhos = configurador.obter_caminhos()
    for nome, caminho in caminhos.items():
        status = "✅" if caminho.exists() else "❌"
        click.echo(f"   {nome}: {caminho} {status}")
    
    # Vozes disponíveis
    click.echo("\n🎤 Vozes disponíveis:")
    vozes = configurador.listar_vozes_disponiveis()
    for categoria, lista_vozes in vozes.items():
        click.echo(f"   {categoria.title()}: {', '.join(lista_vozes)}")


@cli.command()
@click.pass_context
def setup(ctx):
    """
    Configura o sistema pela primeira vez
    """
    click.echo(f"🛠️ Configuração inicial do {NOME_APP}")
    click.echo("=" * 50)
    
    configurador = ctx.obj['configurador']
    
    # Criar estrutura de diretórios
    click.echo("\n📁 Criando estrutura de diretórios...")
    caminhos = configurador.obter_caminhos()
    
    diretorios = ['pasta_saida', 'pasta_temp', 'pasta_logs']
    for dir_nome in diretorios:
        caminho = caminhos[dir_nome]
        caminho.mkdir(parents=True, exist_ok=True)
        click.echo(f"   ✅ {dir_nome}: {caminho}")
    
    # Criar arquivo de configuração de exemplo
    click.echo("\n📝 Criando arquivo de configuração de exemplo...")
    configurador.criar_configuracao_exemplo()
    
    # Testar TTS
    click.echo("\n🎵 Testando Kokoro TTS...")
    try:
        config_tts = configurador.obter_configuracoes_tts()
        gerador = GeradorAudiobook(config_tts)
        click.echo("   ✅ Kokoro TTS funcionando!")
    except Exception as e:
        click.echo(f"   ❌ Erro no TTS: {e}")
    
    click.echo("\n🎉 Configuração inicial concluída!")
    click.echo("💡 Dicas:")
    click.echo("   - Use 'main.py config' para ver as configurações")
    click.echo("   - Use 'main.py arquivo <arquivo.md>' para gerar um audiobook")
    click.echo("   - Use 'main.py pasta <pasta>' para processar uma pasta inteira")


if __name__ == '__main__':
    cli()
