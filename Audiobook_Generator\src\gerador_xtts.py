#!/usr/bin/env python3
"""
Gerador de áudio usando XTTS-v2 da Coqui.
Este módulo implementa o engine XTTS-v2 para geração de audiobooks
com voice cloning e qualidade profissional em português brasileiro.
"""

import os
import time
import tempfile
import logging
from pathlib import Path
from typing import Optional, Union, List
import numpy as np
import soundfile as sf

# Configurar logging
logger = logging.getLogger(__name__)

class GeradorXTTS:
    """
    Gerador de áudio usando XTTS-v2 com voice cloning.
    
    Este gerador oferece qualidade superior ao Kokoro através de:
    - Voice cloning com apenas 6 segundos de áudio de referência
    - Vozes muito mais naturais em português brasileiro
    - Qualidade profissional/comercial
    """
    
    def __init__(self, configuracoes: dict):
        """
        Inicializa o gerador XTTS-v2.
        
        Args:
            configuracoes: Dicionário com configurações do XTTS-v2
        """
        self.config = configuracoes
        self.tts = None
        self.modelo_carregado = False
        self.voz_referencia_cache = {}
        
        # Configurações padrão
        self.model_name = self.config.get('model_name', 'tts_models/multilingual/multi-dataset/xtts_v2')
        self.language = self.config.get('language', 'pt')
        self.speaker_wav = self.config.get('speaker_wav', None)
        self.speed = self.config.get('speed', 1.0)
        self.temperature = self.config.get('temperature', 0.75)
        self.length_penalty = self.config.get('length_penalty', 1.0)
        self.repetition_penalty = self.config.get('repetition_penalty', 5.0)
        
        logger.info(f"GeradorXTTS inicializado com modelo: {self.model_name}")
    
    def _inicializar_xtts(self) -> bool:
        """
        Inicializa o modelo XTTS-v2.
        
        Returns:
            bool: True se inicializado com sucesso
        """
        if self.modelo_carregado:
            return True
            
        try:
            logger.info("Carregando modelo XTTS-v2...")
            start_time = time.time()
            
            from TTS.api import TTS
            self.tts = TTS(self.model_name)
            
            load_time = time.time() - start_time
            logger.info(f"Modelo XTTS-v2 carregado em {load_time:.2f} segundos")
            
            self.modelo_carregado = True
            return True
            
        except Exception as e:
            logger.error(f"Erro ao carregar modelo XTTS-v2: {e}")
            return False
    
    def _criar_voz_referencia_sintetica(self) -> str:
        """
        Cria uma voz de referência sintética para testes.
        
        Returns:
            str: Caminho para o arquivo de voz de referência
        """
        try:
            # Criar arquivo temporário
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                voz_path = temp_file.name
            
            # Criar um tom complexo que simule uma voz
            sample_rate = 24000
            duration = 3.0
            
            t = np.linspace(0, duration, int(sample_rate * duration), False)
            
            # Múltiplas frequências para simular voz humana
            audio = np.sin(440 * 2 * np.pi * t) * 0.3  # Fundamental
            audio += np.sin(660 * 2 * np.pi * t) * 0.2  # Harmônico
            audio += np.sin(880 * 2 * np.pi * t) * 0.1  # Harmônico superior
            
            # Envelope para naturalidade
            envelope = np.exp(-t * 0.5)
            audio = audio * envelope
            
            # Salvar arquivo
            sf.write(voz_path, audio, sample_rate)
            
            logger.debug(f"Voz de referência sintética criada: {voz_path}")
            return voz_path
            
        except Exception as e:
            logger.error(f"Erro ao criar voz de referência sintética: {e}")
            raise
    
    def _obter_voz_referencia(self, speaker_wav: Optional[str] = None) -> str:
        """
        Obtém o caminho para a voz de referência.
        
        Args:
            speaker_wav: Caminho para arquivo de voz específico
            
        Returns:
            str: Caminho para arquivo de voz de referência
        """
        # Usar voz específica se fornecida
        if speaker_wav and os.path.exists(speaker_wav):
            return speaker_wav
        
        # Usar voz padrão da configuração
        if self.speaker_wav and os.path.exists(self.speaker_wav):
            return self.speaker_wav
        
        # Criar voz sintética como fallback
        logger.warning("Nenhuma voz de referência encontrada, criando voz sintética")
        return self._criar_voz_referencia_sintetica()
    
    def gerar_audio_segmento(self, texto: str, speaker_wav: Optional[str] = None) -> np.ndarray:
        """
        Gera áudio para um segmento de texto usando XTTS-v2.
        
        Args:
            texto: Texto para converter em áudio
            speaker_wav: Caminho para arquivo de voz de referência (opcional)
            
        Returns:
            np.ndarray: Array de áudio gerado
        """
        if not self._inicializar_xtts():
            raise RuntimeError("Falha ao inicializar XTTS-v2")
        
        try:
            # Obter voz de referência
            voz_referencia = self._obter_voz_referencia(speaker_wav)
            
            # Criar arquivo temporário para saída
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_output = temp_file.name
            
            try:
                # Gerar áudio com XTTS-v2
                logger.debug(f"Gerando áudio para texto: {texto[:50]}...")
                
                self.tts.tts_to_file(
                    text=texto,
                    language=self.language,
                    speaker_wav=voz_referencia,
                    file_path=temp_output,
                    speed=self.speed
                )
                
                # Carregar áudio gerado
                audio_data, sample_rate = sf.read(temp_output)
                
                logger.debug(f"Áudio gerado: {len(audio_data)} samples, {sample_rate}Hz")
                
                return audio_data
                
            finally:
                # Limpar arquivo temporário
                if os.path.exists(temp_output):
                    os.unlink(temp_output)
                
        except Exception as e:
            logger.error(f"Erro ao gerar áudio com XTTS-v2: {e}")
            raise
    
    def clonar_voz(self, texto: str, speaker_wav: str) -> np.ndarray:
        """
        Gera áudio clonando uma voz específica.
        
        Args:
            texto: Texto para converter em áudio
            speaker_wav: Caminho para arquivo de voz de referência
            
        Returns:
            np.ndarray: Array de áudio gerado com voz clonada
        """
        if not os.path.exists(speaker_wav):
            raise FileNotFoundError(f"Arquivo de voz não encontrado: {speaker_wav}")
        
        logger.info(f"Clonando voz de: {speaker_wav}")
        return self.gerar_audio_segmento(texto, speaker_wav)
    
    def listar_vozes_disponiveis(self) -> List[str]:
        """
        Lista as vozes de referência disponíveis na pasta voices/.
        
        Returns:
            List[str]: Lista de caminhos para arquivos de voz
        """
        vozes_dir = Path("voices")
        if not vozes_dir.exists():
            return []
        
        extensoes_audio = {'.wav', '.mp3', '.flac', '.ogg'}
        vozes = []
        
        for arquivo in vozes_dir.iterdir():
            if arquivo.suffix.lower() in extensoes_audio:
                vozes.append(str(arquivo))
        
        return sorted(vozes)
    
    def obter_informacoes_modelo(self) -> dict:
        """
        Obtém informações sobre o modelo XTTS-v2 carregado.
        
        Returns:
            dict: Informações do modelo
        """
        return {
            'nome': self.model_name,
            'linguagem': self.language,
            'carregado': self.modelo_carregado,
            'voz_padrao': self.speaker_wav,
            'configuracoes': {
                'speed': self.speed,
                'temperature': self.temperature,
                'length_penalty': self.length_penalty,
                'repetition_penalty': self.repetition_penalty
            }
        }
    
    def __del__(self):
        """Limpeza ao destruir o objeto."""
        # Limpar vozes de referência temporárias
        for voz_path in self.voz_referencia_cache.values():
            if os.path.exists(voz_path) and 'tmp' in voz_path:
                try:
                    os.unlink(voz_path)
                except:
                    pass
