#!/usr/bin/env python3
"""
Teste Final do Sistema XTTS-v2
Teste completo do sistema dual-engine com comparação de qualidade.
"""

import sys
import time
import tempfile
from pathlib import Path

def criar_nota_teste():
    """Cria uma nota de teste para o audiobook"""
    
    conteudo_nota = """# Teste do Sistema XTTS-v2

Este é um teste completo do novo sistema de geração de audiobooks.

## Características do XTTS-v2

O XTTS-v2 oferece:

- **Voice cloning** com apenas 6 segundos de áudio
- **Qualidade profissional** em português brasileiro  
- **Naturalidade excepcional** comparado ao Kokoro
- **Compatibilidade total** com o sistema existente

## Teste de Diferentes Elementos

### Listas e Formatação

1. Primeiro item da lista
2. Segundo item com **negrito**
3. Terceiro item com *itálico*

### Citações

> Esta é uma citação de teste para verificar como o sistema processa diferentes tipos de formatação de texto.

### Texto Técnico

O sistema utiliza transformers>=4.33.0 e coqui-tts>=0.26.0 para funcionar corretamente.

## Conclusão

Este teste verifica se o sistema dual-engine está funcionando perfeitamente com fallback automático entre XTTS-v2 e Kokoro TTS.
"""
    
    # Criar arquivo temporário
    with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False, encoding='utf-8') as f:
        f.write(conteudo_nota)
        return Path(f.name)

def testar_sistema_completo():
    """Executa teste completo do sistema"""
    
    print("🧪 Teste Final do Sistema XTTS-v2")
    print("=" * 50)
    
    try:
        # Criar nota de teste
        print("📝 Criando nota de teste...")
        nota_teste = criar_nota_teste()
        print(f"✅ Nota criada: {nota_teste}")
        
        # Teste 1: Engine automático (XTTS-v2 primary)
        print("\n🚀 Teste 1: Engine Automático (XTTS-v2)")
        print("-" * 30)
        
        import subprocess
        
        cmd1 = [
            sys.executable, "main.py", "arquivo", str(nota_teste),
            "--engine", "auto",
            "--output", "temp/teste_auto"
        ]
        
        print(f"💻 Comando: {' '.join(cmd1)}")
        
        start_time = time.time()
        result1 = subprocess.run(cmd1, capture_output=True, text=True, cwd=".")
        test1_time = time.time() - start_time
        
        if result1.returncode == 0:
            print(f"✅ Teste 1 concluído em {test1_time:.2f} segundos")
            print("📊 Saída:")
            for linha in result1.stdout.split('\n')[-10:]:  # Últimas 10 linhas
                if linha.strip():
                    print(f"   {linha}")
        else:
            print(f"❌ Teste 1 falhou:")
            print(f"   {result1.stderr}")
        
        # Teste 2: Forçar Kokoro
        print("\n🎵 Teste 2: Engine Kokoro (Fallback)")
        print("-" * 30)
        
        cmd2 = [
            sys.executable, "main.py", "arquivo", str(nota_teste),
            "--engine", "kokoro",
            "--voice", "pf_dora",
            "--speed", "0.85",
            "--output", "temp/teste_kokoro"
        ]
        
        print(f"💻 Comando: {' '.join(cmd2)}")
        
        start_time = time.time()
        result2 = subprocess.run(cmd2, capture_output=True, text=True, cwd=".")
        test2_time = time.time() - start_time
        
        if result2.returncode == 0:
            print(f"✅ Teste 2 concluído em {test2_time:.2f} segundos")
            print("📊 Saída:")
            for linha in result2.stdout.split('\n')[-10:]:
                if linha.strip():
                    print(f"   {linha}")
        else:
            print(f"❌ Teste 2 falhou:")
            print(f"   {result2.stderr}")
        
        # Teste 3: Comando vozes
        print("\n🎭 Teste 3: Gerenciador de Vozes")
        print("-" * 30)
        
        cmd3 = [sys.executable, "main.py", "vozes"]
        result3 = subprocess.run(cmd3, capture_output=True, text=True, cwd=".")
        
        if result3.returncode == 0:
            print("✅ Comando vozes funcionando:")
            for linha in result3.stdout.split('\n'):
                if linha.strip():
                    print(f"   {linha}")
        else:
            print(f"❌ Comando vozes falhou: {result3.stderr}")
        
        # Verificar arquivos gerados
        print("\n📁 Verificando arquivos gerados:")
        
        pasta_auto = Path("temp/teste_auto")
        pasta_kokoro = Path("temp/teste_kokoro")
        
        if pasta_auto.exists():
            arquivos_auto = list(pasta_auto.glob("*.wav"))
            if arquivos_auto:
                arquivo_auto = arquivos_auto[0]
                tamanho_auto = arquivo_auto.stat().st_size
                print(f"✅ XTTS-v2: {arquivo_auto.name} ({tamanho_auto:,} bytes)")
            else:
                print("⚠️ XTTS-v2: Nenhum arquivo WAV encontrado")
        
        if pasta_kokoro.exists():
            arquivos_kokoro = list(pasta_kokoro.glob("*.wav"))
            if arquivos_kokoro:
                arquivo_kokoro = arquivos_kokoro[0]
                tamanho_kokoro = arquivo_kokoro.stat().st_size
                print(f"✅ Kokoro: {arquivo_kokoro.name} ({tamanho_kokoro:,} bytes)")
            else:
                print("⚠️ Kokoro: Nenhum arquivo WAV encontrado")
        
        # Comparação de performance
        print(f"\n⚡ Comparação de Performance:")
        print(f"   XTTS-v2: {test1_time:.2f}s")
        print(f"   Kokoro:  {test2_time:.2f}s")
        
        if test1_time > 0 and test2_time > 0:
            if test1_time < test2_time:
                print(f"   🏆 XTTS-v2 foi {test2_time/test1_time:.1f}x mais rápido")
            else:
                print(f"   🏆 Kokoro foi {test1_time/test2_time:.1f}x mais rápido")
        
        print("\n" + "=" * 50)
        print("🎉 TESTE FINAL CONCLUÍDO!")
        
        # Resumo final
        sucessos = 0
        if result1.returncode == 0:
            sucessos += 1
        if result2.returncode == 0:
            sucessos += 1
        if result3.returncode == 0:
            sucessos += 1
        
        print(f"📊 Sucessos: {sucessos}/3 testes")
        
        if sucessos == 3:
            print("🚀 SISTEMA TOTALMENTE FUNCIONAL!")
            print("✅ XTTS-v2 implementado com sucesso")
            print("✅ Sistema dual-engine operacional")
            print("✅ CLI atualizado com novas opções")
            print("✅ Fallback automático funcionando")
        else:
            print("⚠️ Alguns testes falharam - verificar logs acima")
        
        return sucessos == 3
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Limpar arquivo de teste
        try:
            if 'nota_teste' in locals() and nota_teste.exists():
                nota_teste.unlink()
                print(f"🧹 Arquivo de teste removido: {nota_teste}")
        except:
            pass

def main():
    """Função principal"""
    print("🎯 Iniciando teste final do sistema XTTS-v2...")
    print()
    
    sucesso = testar_sistema_completo()
    
    if sucesso:
        print("\n🎊 IMPLEMENTAÇÃO XTTS-v2 CONCLUÍDA COM SUCESSO!")
        print("\n📖 Como usar:")
        print("   # Engine automático (recomendado)")
        print("   python main.py arquivo 'sua_nota.md'")
        print()
        print("   # Com voice cloning")
        print("   python main.py arquivo 'nota.md' --clone-voice 'voz.wav'")
        print()
        print("   # Forçar Kokoro")
        print("   python main.py arquivo 'nota.md' --engine kokoro")
        print()
        print("   # Gerenciar vozes")
        print("   python main.py vozes")
    else:
        print("\n🔧 Alguns problemas encontrados - verificar logs acima")
    
    return sucesso

if __name__ == "__main__":
    main()
