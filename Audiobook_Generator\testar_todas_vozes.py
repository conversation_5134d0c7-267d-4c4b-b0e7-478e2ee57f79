#!/usr/bin/env python3
"""
Script para testar todas as 9 amostras de vozes com XTTS-v2
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def testar_todas_vozes():
    """Testa todas as 9 amostras de vozes"""
    
    print("🎭 <PERSON>and<PERSON> as Amostras de Vozes com XTTS-v2")
    print("=" * 60)
    
    # Verificar pasta de amostras
    pasta_amostras = Path("amostras")
    if not pasta_amostras.exists():
        print("❌ Pasta 'amostras' não encontrada!")
        return False
    
    # Listar arquivos de voz
    vozes = list(pasta_amostras.glob("*.wav"))
    if not vozes:
        print("❌ Nenhum arquivo .wav encontrado na pasta amostras!")
        return False
    
    print(f"🎤 Encontradas {len(vozes)} amostras de voz:")
    for i, voz in enumerate(vozes, 1):
        print(f"   {i}. {voz.name}")
    
    print(f"\n📝 Arquivo de teste: teste_vozes.md")
    print(f"📁 Saída: temp/teste_vozes/")
    
    # Criar pasta de saída
    pasta_saida = Path("temp/teste_vozes")
    pasta_saida.mkdir(parents=True, exist_ok=True)
    
    sucessos = 0
    falhas = 0
    
    print(f"\n🚀 Iniciando testes...")
    print("-" * 60)
    
    for i, voz in enumerate(vozes, 1):
        print(f"\n🎯 Teste {i}/9: {voz.name}")
        print(f"⏰ {time.strftime('%H:%M:%S')}")
        
        try:
            # Comando para gerar audiobook com esta voz
            cmd = [
                sys.executable, "main.py", "arquivo", "teste_vozes.md",
                "--engine", "xtts_v2",
                "--clone-voice", str(voz),
                "--output", f"temp/teste_vozes/voz_{i}"
            ]
            
            print(f"💻 Executando: {' '.join(cmd[-6:])}")  # Mostrar só as últimas partes
            
            # Executar comando
            start_time = time.time()
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=".")
            end_time = time.time()
            
            if result.returncode == 0:
                print(f"✅ Sucesso em {end_time - start_time:.1f}s")
                sucessos += 1
                
                # Verificar se arquivo foi criado
                arquivo_gerado = pasta_saida / f"voz_{i}" / "teste_vozes.wav"
                if arquivo_gerado.exists():
                    tamanho = arquivo_gerado.stat().st_size
                    print(f"📊 Arquivo: {tamanho:,} bytes")
                else:
                    print("⚠️ Arquivo não encontrado")
                    
            else:
                print(f"❌ Falha:")
                # Mostrar apenas as últimas linhas do erro
                linhas_erro = result.stderr.split('\n')[-3:]
                for linha in linhas_erro:
                    if linha.strip():
                        print(f"   {linha}")
                falhas += 1
                
        except Exception as e:
            print(f"❌ Erro: {e}")
            falhas += 1
    
    # Resumo final
    print("\n" + "=" * 60)
    print("📊 RESUMO DOS TESTES")
    print("=" * 60)
    print(f"✅ Sucessos: {sucessos}/9")
    print(f"❌ Falhas: {falhas}/9")
    print(f"📁 Arquivos em: temp/teste_vozes/")
    
    if sucessos > 0:
        print(f"\n🎧 Para ouvir os resultados:")
        print(f"   Vá para: temp/teste_vozes/")
        print(f"   Cada pasta voz_X contém o audiobook gerado")
        
        print(f"\n🏆 Vozes que funcionaram:")
        for i in range(1, 10):
            arquivo = pasta_saida / f"voz_{i}" / "teste_vozes.wav"
            if arquivo.exists():
                voz_original = vozes[i-1].name
                print(f"   ✅ Voz {i}: {voz_original}")
    
    return sucessos > 0

def main():
    """Função principal"""
    
    print("🎯 Iniciando teste de todas as amostras de vozes...")
    print()
    
    sucesso = testar_todas_vozes()
    
    if sucesso:
        print("\n🎉 Testes concluídos!")
        print("🎧 Ouça os resultados para escolher sua voz favorita")
        print("\n💡 Para usar uma voz específica:")
        print('   python main.py arquivo "seu_resumo.md" --clone-voice "amostras/Voz-X.wav"')
    else:
        print("\n🔧 Todos os testes falharam - verificar configuração")
    
    return sucesso

if __name__ == "__main__":
    main()
