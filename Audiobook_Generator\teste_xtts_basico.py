#!/usr/bin/env python3
"""
Teste básico do XTTS-v2 para verificar instalação e funcionamento.
Este script testa o modelo XTTS-v2 com texto em português brasileiro.
"""

import os
import sys
import time
from pathlib import Path

def testar_xtts_basico():
    """Testa o funcionamento básico do XTTS-v2"""
    
    print("🧪 Teste Básico XTTS-v2")
    print("=" * 50)
    
    try:
        # Importar TTS
        print("📦 Importando TTS...")
        from TTS.api import TTS
        print("✅ TTS importado com sucesso!")
        
        # Listar modelos disponíveis
        print("\n📋 Verificando modelos disponíveis...")
        try:
            # Tentar listar modelos XTTS
            print("🔍 Procurando modelos XTTS...")
            
            # Inicializar TTS com modelo XTTS-v2
            print("\n🚀 Inicializando XTTS-v2...")
            model_name = "tts_models/multilingual/multi-dataset/xtts_v2"
            
            print(f"📥 Carregando modelo: {model_name}")
            print("⏳ Isso pode demorar alguns minutos na primeira vez...")
            
            start_time = time.time()
            tts = TTS(model_name)
            load_time = time.time() - start_time
            
            print(f"✅ Modelo carregado em {load_time:.2f} segundos!")
            
            # Teste básico de síntese
            print("\n🎵 Testando síntese de voz...")
            
            texto_teste = "Olá! Este é um teste do XTTS-v2 em português brasileiro. O sistema está funcionando perfeitamente."
            
            # Criar pasta de teste se não existir
            pasta_teste = Path("temp/teste_xtts")
            pasta_teste.mkdir(parents=True, exist_ok=True)
            
            arquivo_saida = pasta_teste / "teste_xtts_basico.wav"
            
            print(f"📝 Texto: {texto_teste}")
            print(f"💾 Salvando em: {arquivo_saida}")
            
            # Gerar áudio usando voice cloning com voz de referência
            start_time = time.time()

            # XTTS-v2 requer voice cloning - vamos usar uma abordagem mais simples
            try:
                print("🎤 XTTS-v2 requer voice cloning...")
                print("🔧 Tentando usar voz de referência de exemplo...")

                # Tentar usar uma das vozes de exemplo do próprio TTS
                try:
                    # Verificar se há vozes de exemplo disponíveis
                    print("🔍 Procurando vozes de exemplo...")

                    # Usar uma URL de exemplo de voz em português
                    # Esta é uma abordagem temporária para teste
                    import urllib.request
                    import tempfile

                    # Criar arquivo temporário para voz de referência
                    with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                        voz_referencia_path = temp_file.name

                    # Por enquanto, vamos criar um arquivo de áudio simples usando numpy
                    print("🎵 Criando voz de referência sintética...")
                    import numpy as np
                    import soundfile as sf

                    # Criar um tom simples como voz de referência (apenas para teste)
                    sample_rate = 24000
                    duration = 3.0  # 3 segundos
                    frequency = 440  # Frequência A4

                    t = np.linspace(0, duration, int(sample_rate * duration), False)
                    # Criar um tom mais complexo que simule uma voz
                    audio = np.sin(frequency * 2 * np.pi * t) * 0.3
                    audio += np.sin(frequency * 1.5 * 2 * np.pi * t) * 0.2
                    audio += np.sin(frequency * 0.8 * 2 * np.pi * t) * 0.1

                    # Adicionar envelope para soar mais natural
                    envelope = np.exp(-t * 0.5)
                    audio = audio * envelope

                    # Salvar como arquivo WAV
                    sf.write(voz_referencia_path, audio, sample_rate)

                    print(f"✅ Voz de referência criada: {voz_referencia_path}")

                    # Agora usar XTTS-v2 com voice cloning
                    print("🎵 Gerando áudio com XTTS-v2 + voice cloning...")

                    tts.tts_to_file(
                        text=texto_teste,
                        language="pt",
                        speaker_wav=voz_referencia_path,
                        file_path=str(arquivo_saida)
                    )

                except Exception as e:
                    print(f"⚠️ Erro ao criar voz de referência: {e}")
                    print("🔄 Tentando abordagem alternativa...")

                    # Fallback: tentar sem voice cloning (pode falhar, mas vamos tentar)
                    tts.tts_to_file(
                        text=texto_teste,
                        language="pt",
                        file_path=str(arquivo_saida)
                    )
                synthesis_time = time.time() - start_time
                
                print(f"✅ Áudio gerado em {synthesis_time:.2f} segundos!")
                print(f"📁 Arquivo salvo: {arquivo_saida}")
                
                # Verificar se arquivo foi criado
                if arquivo_saida.exists():
                    tamanho = arquivo_saida.stat().st_size
                    print(f"📊 Tamanho do arquivo: {tamanho:,} bytes")
                    
                    if tamanho > 1000:  # Arquivo deve ter pelo menos 1KB
                        print("🎉 TESTE BÁSICO PASSOU!")
                        return True
                    else:
                        print("⚠️ Arquivo muito pequeno, pode haver problema")
                        return False
                else:
                    print("❌ Arquivo não foi criado")
                    return False
                    
            except Exception as e:
                print(f"❌ Erro na síntese: {e}")
                return False
                
        except Exception as e:
            print(f"❌ Erro ao carregar modelo: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ Erro de importação: {e}")
        print("💡 Certifique-se de que o coqui-tts está instalado:")
        print("   pip install coqui-tts")
        return False
        
    except Exception as e:
        print(f"❌ Erro inesperado: {e}")
        return False

def main():
    """Função principal"""
    print("🎯 Iniciando teste básico do XTTS-v2...")
    print()
    
    sucesso = testar_xtts_basico()
    
    print("\n" + "=" * 50)
    if sucesso:
        print("🎉 TESTE CONCLUÍDO COM SUCESSO!")
        print("✅ XTTS-v2 está funcionando corretamente")
        print("🚀 Pronto para implementação no sistema principal")
    else:
        print("❌ TESTE FALHOU")
        print("🔧 Verifique os erros acima e tente novamente")
    
    return sucesso

if __name__ == "__main__":
    main()
