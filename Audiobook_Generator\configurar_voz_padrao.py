#!/usr/bin/env python3
"""
Script para configurar uma voz padrão das suas 9 amostras
"""

import os
import sys
import yaml
from pathlib import Path

def listar_vozes_disponiveis():
    """Lista todas as vozes disponíveis na pasta amostras"""
    
    pasta_amostras = Path("amostras")
    if not pasta_amostras.exists():
        print("❌ Pasta 'amostras' não encontrada!")
        return []
    
    vozes = list(pasta_amostras.glob("*.wav"))
    return sorted(vozes)

def configurar_voz_padrao():
    """Permite escolher e configurar uma voz padrão"""
    
    print("🎭 Configurador de Voz Padrão")
    print("=" * 40)
    
    # Listar vozes disponíveis
    vozes = listar_vozes_disponiveis()
    if not vozes:
        print("❌ Nenhuma voz encontrada na pasta amostras/")
        return False
    
    print(f"🎤 Vozes disponíveis ({len(vozes)}):")
    for i, voz in enumerate(vozes, 1):
        print(f"   {i}. {voz.name}")
    
    # Solicitar escolha
    while True:
        try:
            print(f"\n🎯 Escolha uma voz (1-{len(vozes)}) ou 0 para cancelar:")
            escolha = input(">>> ").strip()
            
            if escolha == "0":
                print("❌ Cancelado pelo usuário")
                return False
            
            indice = int(escolha) - 1
            if 0 <= indice < len(vozes):
                voz_escolhida = vozes[indice]
                break
            else:
                print(f"❌ Número inválido! Digite entre 1 e {len(vozes)}")
                
        except ValueError:
            print("❌ Digite apenas números!")
    
    # Confirmar escolha
    print(f"\n✅ Voz escolhida: {voz_escolhida.name}")
    
    # Atualizar configuração
    try:
        # Carregar configuração atual
        config_path = Path("config/config.yaml")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # Atualizar voz padrão
        if 'xtts_v2' not in config:
            config['xtts_v2'] = {}
        
        config['xtts_v2']['speaker_wav'] = str(voz_escolhida)
        
        # Salvar configuração
        with open(config_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        print(f"✅ Configuração salva!")
        print(f"📁 Voz padrão: {voz_escolhida.name}")
        
        # Mostrar como usar
        print(f"\n🚀 Agora você pode usar:")
        print(f"   python main.py arquivo \"seu_resumo.md\"")
        print(f"   python main.py pasta \"sua_pasta\"")
        print(f"\n🎯 A voz {voz_escolhida.name} será usada automaticamente!")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao salvar configuração: {e}")
        return False

def mostrar_configuracao_atual():
    """Mostra a configuração atual de voz"""
    
    try:
        config_path = Path("config/config.yaml")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        voz_atual = config.get('xtts_v2', {}).get('speaker_wav', 'Nenhuma configurada')
        
        print(f"🎤 Voz padrão atual: {Path(voz_atual).name if voz_atual != 'Nenhuma configurada' else voz_atual}")
        
    except Exception as e:
        print(f"⚠️ Erro ao ler configuração: {e}")

def main():
    """Função principal"""
    
    print("🎯 Configurador de Voz Padrão XTTS-v2")
    print()
    
    # Mostrar configuração atual
    mostrar_configuracao_atual()
    print()
    
    # Configurar nova voz
    sucesso = configurar_voz_padrao()
    
    if sucesso:
        print("\n🎉 Configuração concluída!")
        print("🎧 Teste com um arquivo pequeno para verificar a qualidade")
    else:
        print("\n🔧 Configuração não alterada")
    
    return sucesso

if __name__ == "__main__":
    main()
